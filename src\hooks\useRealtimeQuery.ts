import { useEffect, useMemo } from 'react';
import {
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from '@tanstack/react-query';
import type { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { useSupabaseClient } from '@/lib/supabase-clerk';
import type { Database } from '@/types/supabase';
import RealtimeDataService from '@/lib/services/realtime-data.service';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useUserDatabaseId } from '@/features/shared/hooks/useUserDatabaseId';
import type { Ticket } from '@/features/ticketing/models/ticket.schema';

// Type for database row
type TicketRow = Database['public']['Tables']['tickets']['Row'];
type MessageRow = Database['public']['Tables']['ticket_messages']['Row'];

// Simple rate limiting for missing ticket fetches
const missingTicketFetches = new Map<string, number>();

function canFetchMissingTicket(ticketId: string): boolean {
  const now = Date.now();
  const lastFetch = missingTicketFetches.get(ticketId) || 0;

  // Allow fetch if more than 6 seconds have passed (10 fetches per minute max)
  return now - lastFetch > 6000;
}

function trackMissingTicketFetch(ticketId: string): void {
  missingTicketFetches.set(ticketId, Date.now());

  // Clean up old entries every 100 fetches
  if (missingTicketFetches.size > 100) {
    const cutoff = Date.now() - 60000; // 1 minute ago
    for (const [id, timestamp] of missingTicketFetches.entries()) {
      if (timestamp < cutoff) {
        missingTicketFetches.delete(id);
      }
    }
  }
}

/**
 * ✅ Recommended Pattern: useRealtimeQuery() Custom Hook (Modern Supabase + React Query)
 *
 * Based on latest 2025 patterns from Docs/LatestPatterns.md
 *
 * This hook encapsulates:
 * - Fetching data with React Query
 * - Subscribing to Supabase real-time changes
 * - Automatically updating the cache when DB changes occur
 *
 * Benefits:
 * - Minimal, modern, robust
 * - Clean separation of concerns
 * - No manual subscription management
 * - Automatic cache updates on real-time events
 */
export function useRealtimeQuery<T extends { id: string }>(
  queryKey: string[],
  fetchFn: () => Promise<T[]>,
  table: string,
  options?: {
    filter?: string;
    schema?: string;
    queryOptions?: Omit<UseQueryOptions<T[]>, 'queryKey' | 'queryFn'>;
  }
) {
  const { supabase } = useSupabaseClient();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { userDatabaseId } = useUserDatabaseId();

  // Initialize RealtimeDataService for proper user data transformation
  const realtimeDataService = useMemo(
    () => new RealtimeDataService(supabase),
    [supabase]
  );

  // Memoize queryKey to prevent subscription churn
  const stableQueryKey = useMemo(() => queryKey, [queryKey]);

  // Memoize options to prevent subscription churn
  const stableOptions = useMemo(() => options, [options]);

  // React Query for data fetching
  const query = useQuery({
    queryKey: stableQueryKey,
    queryFn: fetchFn,
    ...stableOptions?.queryOptions,
  });

  // ✅ Consolidated real-time subscription with tenant-aware channel naming
  useEffect(() => {
    if (!user) return;

    // Extract tenant UUID from filter for consistent channel naming
    const tenantUuid = stableOptions?.filter?.match(
      /tenant_id=eq\.([^&]+)/
    )?.[1];
    const channelName = tenantUuid
      ? `${table}-${tenantUuid}`
      : `realtime:${table}`;

    const channel = supabase
      .channel(channelName)
      .on(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        'postgres_changes' as any,
        {
          event: '*',
          schema: stableOptions?.schema || 'public',
          table,
          filter: stableOptions?.filter,
        },
        async (
          payload: RealtimePostgresChangesPayload<Record<string, unknown>>
        ) => {
          console.log('Real-time change received:', payload);

          const { eventType, new: newRow, old: oldRow } = payload;

          // Skip updates for current user's own actions to prevent duplicates
          if (userDatabaseId && eventType === 'INSERT') {
            if (table === 'tickets') {
              const ticketRow = newRow as TicketRow;
              if (ticketRow.created_by === userDatabaseId) return;
            } else if (table === 'ticket_messages') {
              const messageRow = newRow as MessageRow;
              if (messageRow.author_id === userDatabaseId) return;
            }
          }

          try {
            // Transform data first (async operations)
            let transformedRow: T | null = null;

            if (eventType === 'INSERT' || eventType === 'UPDATE') {
              if (table === 'tickets') {
                const transformed =
                  await realtimeDataService.transformTicketRow(
                    newRow as TicketRow
                  );
                transformedRow = transformed as unknown as T;
              } else if (table === 'ticket_messages') {
                const transformed =
                  await realtimeDataService.transformMessageRow(
                    newRow as MessageRow
                  );
                transformedRow = transformed as unknown as T;
              } else {
                transformedRow = newRow as T;
              }
            }

            // Simple cache update with smart missing ticket handling
            queryClient.setQueryData(stableQueryKey, (oldData: T[] = []) => {
              switch (eventType) {
                case 'INSERT': {
                  if (!transformedRow) return oldData;

                  // Check for duplicates
                  const existingIndex = oldData.findIndex(
                    (item) => item.id === transformedRow.id
                  );
                  if (existingIndex !== -1) {
                    const newData = [...oldData];
                    newData[existingIndex] = transformedRow;
                    return newData;
                  }

                  // Insert new item
                  return table === 'ticket_messages'
                    ? [...oldData, transformedRow] // Messages: append
                    : [transformedRow, ...oldData]; // Tickets: prepend
                }

                case 'UPDATE': {
                  if (!transformedRow) return oldData;

                  const existingIndex = oldData.findIndex(
                    (item) => item.id === transformedRow.id
                  );

                  if (existingIndex !== -1) {
                    // Ticket exists - update it
                    const newData = [...oldData];
                    newData[existingIndex] = transformedRow;

                    // For tickets, sort by updated_at to move recently updated to top
                    // Only sort if we have more than 1 item for performance
                    if (table === 'tickets' && newData.length > 1) {
                      return newData.sort((a, b) => {
                        const aUpdated = (a as unknown as Ticket).updatedAt;
                        const bUpdated = (b as unknown as Ticket).updatedAt;
                        return (
                          new Date(bUpdated).getTime() -
                          new Date(aUpdated).getTime()
                        );
                      });
                    }

                    return newData;
                  } else if (table === 'tickets') {
                    // CORE FIX: Ticket doesn't exist in cache but was updated
                    // Add it to the top since it was recently updated
                    console.log(
                      `🔄 Adding missing updated ticket ${transformedRow.id} to cache`
                    );
                    const newData = [transformedRow, ...oldData];

                    // Sort by updated_at for proper ordering
                    // Only sort if we have more than 1 item for performance
                    if (newData.length > 1) {
                      return newData.sort((a, b) => {
                        const aUpdated = (a as unknown as Ticket).updatedAt;
                        const bUpdated = (b as unknown as Ticket).updatedAt;
                        return (
                          new Date(bUpdated).getTime() -
                          new Date(aUpdated).getTime()
                        );
                      });
                    }
                    return newData;
                  }

                  // For non-tickets, only update if exists
                  return oldData;
                }

                case 'DELETE':
                  return oldData.filter((item) => item.id !== (oldRow as T).id);

                default:
                  return oldData;
              }
            });

            // CRITICAL FIX: Unified cache synchronization for ticket updates
            if (
              eventType === 'UPDATE' &&
              table === 'tickets' &&
              transformedRow
            ) {
              const ticketRow = newRow as TicketRow;
              const tenantUuid = ticketRow.tenant_id;
              const ticketId = ticketRow.id;
              const updatedTicket = transformedRow as unknown as Ticket;

              console.log(
                `🔄 Synchronizing ticket ${ticketId} across all caches`
              );

              // CRITICAL FIX: Update ALL related caches directly instead of invalidating
              // This ensures list and detail views stay synchronized

              // Helper function to merge only changed fields
              const mergeChangedFields = (
                existingTicket: Ticket,
                updatedTicket: Ticket
              ): Ticket => {
                const merged = { ...existingTicket };
                let hasChanges = false;

                // Only update fields that have actually changed
                Object.keys(updatedTicket).forEach((key) => {
                  const typedKey = key as keyof Ticket;
                  if (existingTicket[typedKey] !== updatedTicket[typedKey]) {
                    (merged as Record<string, unknown>)[typedKey] =
                      updatedTicket[typedKey];
                    hasChanges = true;
                  }
                });

                return hasChanges ? merged : existingTicket;
              };

              // 1. Update all ticket list caches
              queryClient.setQueriesData(
                {
                  queryKey: ['tickets', tenantUuid, 'list'],
                  exact: false,
                },
                (oldListData: Ticket[] | undefined) => {
                  if (!oldListData) return oldListData;

                  const existingIndex = oldListData.findIndex(
                    (ticket) => ticket.id === ticketId
                  );

                  if (existingIndex !== -1) {
                    // Update existing ticket in list with selective field updates
                    const newListData = [...oldListData];
                    const existingTicket = newListData[existingIndex];
                    if (existingTicket) {
                      newListData[existingIndex] = mergeChangedFields(
                        existingTicket,
                        updatedTicket
                      );
                    } else {
                      newListData[existingIndex] = updatedTicket;
                    }

                    // Sort by updated_at to move recently updated to top
                    return newListData.sort((a, b) => {
                      return (
                        new Date(b.updatedAt).getTime() -
                        new Date(a.updatedAt).getTime()
                      );
                    });
                  } else {
                    // Add missing ticket to top of list
                    return [updatedTicket, ...oldListData];
                  }
                }
              );

              // 2. Update ticket detail cache with selective field updates
              queryClient.setQueryData(
                ['tickets', tenantUuid, 'detail', ticketId],
                (oldDetailData: Ticket[] | undefined) => {
                  if (
                    !oldDetailData ||
                    oldDetailData.length === 0 ||
                    !oldDetailData[0]
                  ) {
                    return [updatedTicket]; // Store as array to match useRealtimeQuery format
                  }

                  const existingTicket = oldDetailData[0];
                  const mergedTicket = mergeChangedFields(
                    existingTicket,
                    updatedTicket
                  );
                  return [mergedTicket];
                }
              );

              // 3. Update realtime-tickets cache if it exists
              queryClient.setQueriesData(
                {
                  queryKey: ['realtime-tickets', tenantUuid],
                  exact: false,
                },
                (oldRealtimeData: Ticket[] | undefined) => {
                  if (!oldRealtimeData) return oldRealtimeData;

                  const existingIndex = oldRealtimeData.findIndex(
                    (ticket) => ticket.id === ticketId
                  );

                  if (existingIndex !== -1) {
                    const newRealtimeData = [...oldRealtimeData];
                    const existingTicket = newRealtimeData[existingIndex];
                    if (existingTicket) {
                      newRealtimeData[existingIndex] = mergeChangedFields(
                        existingTicket,
                        updatedTicket
                      );
                    } else {
                      newRealtimeData[existingIndex] = updatedTicket;
                    }
                    return newRealtimeData.sort((a, b) => {
                      return (
                        new Date(b.updatedAt).getTime() -
                        new Date(a.updatedAt).getTime()
                      );
                    });
                  } else {
                    return [updatedTicket, ...oldRealtimeData];
                  }
                }
              );

              // Log status changes for debugging
              const oldTicketRow = oldRow as TicketRow;
              if (oldTicketRow?.status !== ticketRow.status) {
                console.log(
                  `✅ Status synchronized: ${oldTicketRow?.status} → ${ticketRow.status} for ticket ${ticketId}`
                );
              }
            }

            // Simple missing ticket handling with rate limiting
            if (
              eventType === 'UPDATE' &&
              table === 'tickets' &&
              !transformedRow &&
              newRow
            ) {
              const ticketRow = newRow as TicketRow;
              const tenantId = ticketRow.tenant_id;
              const ticketId = ticketRow.id;

              // Check if ticket is in current cache
              const currentData = queryClient.getQueryData(stableQueryKey) as
                | T[]
                | undefined;
              const ticketInCache = currentData?.some(
                (item) => item.id === ticketId
              );

              if (!ticketInCache && canFetchMissingTicket(ticketId)) {
                console.log(`🔍 Fetching missing ticket ${ticketId} from API`);
                trackMissingTicketFetch(ticketId);

                // Simple fetch with timeout
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

                fetch(`/api/tickets/${ticketId}?tenant_id=${tenantId}`, {
                  signal: controller.signal,
                })
                  .then((response) => {
                    clearTimeout(timeoutId);
                    return response.ok ? response.json() : null;
                  })
                  .then((data) => {
                    if (data?.data) {
                      const fetchedTicket = data.data;

                      // Add to cache with proper sorting
                      queryClient.setQueryData(
                        stableQueryKey,
                        (oldData: T[] = []) => {
                          const stillMissing = !oldData.some(
                            (item) => item.id === ticketId
                          );

                          if (stillMissing) {
                            console.log(
                              `✅ Added missing ticket ${ticketId} to cache`
                            );
                            const newData = [
                              fetchedTicket as unknown as T,
                              ...oldData,
                            ];

                            // Sort by updated_at for proper ordering
                            // Only sort if we have more than 1 item for performance
                            if (table === 'tickets' && newData.length > 1) {
                              return newData.sort((a, b) => {
                                const aUpdated = (a as unknown as Ticket)
                                  .updatedAt;
                                const bUpdated = (b as unknown as Ticket)
                                  .updatedAt;
                                return (
                                  new Date(bUpdated).getTime() -
                                  new Date(aUpdated).getTime()
                                );
                              });
                            }

                            return newData;
                          }

                          return oldData;
                        }
                      );
                    }
                  })
                  .catch((error) => {
                    clearTimeout(timeoutId);
                    if (error.name !== 'AbortError') {
                      console.warn(
                        `⚠️ Failed to fetch missing ticket ${ticketId}:`,
                        error
                      );
                    }
                  });
              }
            }
          } catch (error) {
            console.error('Real-time update error:', error);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [
    supabase,
    stableQueryKey,
    table,
    stableOptions,
    user,
    userDatabaseId,
    queryClient,
    realtimeDataService,
  ]);

  return query;
}

// Types needed for tickets
interface RoleBasedFilterContext {
  tenantId: string;
  role: string;
  userId?: string;
  email?: string;
}

interface TicketFilterOptions {
  status?: string;
  priority?: string;
  assignedTo?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  roleFilter?: 'new' | 'assigned' | 'all';
}

/**
 * Hook to resolve tenant UUID from subdomain
 */
export function useTenantUuid(tenantId: string) {
  const { supabase } = useSupabaseClient();

  return useQuery({
    queryKey: ['tenant-uuid', tenantId],
    queryFn: async () => {
      // If already a UUID, return it
      if (
        tenantId.match(
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
        )
      ) {
        return tenantId;
      }

      // Convert subdomain to UUID
      const { data: tenantData, error: tenantError } = await supabase
        .from('tenants')
        .select('id')
        .eq('subdomain', tenantId)
        .single();

      if (tenantError || !tenantData) {
        throw new Error(`Tenant '${tenantId}' not found`);
      }

      return tenantData.id;
    },
    staleTime: 1000 * 60 * 60, // 1 hour - tenant UUIDs rarely change
    gcTime: 1000 * 60 * 60 * 24, // 24 hours
    enabled: !!tenantId,
  });
}

/**
 * Specialized hook for tickets with real-time updates
 * Compatible with useTickets signature for drop-in replacement
 */
export function useRealtimeTickets(
  context: RoleBasedFilterContext,
  options?: TicketFilterOptions & { enabled?: boolean }
) {
  const { supabase } = useSupabaseClient();
  const { enabled = true, ...filterOptions } = options || {};

  // Resolve tenant UUID first
  const tenantUuidQuery = useTenantUuid(context.tenantId);
  const tenantUuid = tenantUuidQuery.data;

  // Memoize filterOptions to prevent subscription churn
  const stableFilterOptions = useMemo(() => filterOptions, [filterOptions]);

  // Extract complex expression for dependency array
  const stableFilterOptionsString = JSON.stringify(stableFilterOptions || {});

  // Memoize queryKey to prevent subscription churn
  const queryKey = useMemo(
    () => [
      'tickets',
      tenantUuid || 'loading',
      'list',
      stableFilterOptionsString,
    ],
    [tenantUuid, stableFilterOptionsString]
  );

  return useRealtimeQuery(
    queryKey,
    async () => {
      if (!tenantUuid) {
        throw new Error('Tenant UUID not resolved');
      }

      console.log('🔍 Fetching tickets for tenant UUID:', tenantUuid);

      // Build the same query as the original API with specific foreign key relationships
      let query = supabase
        .from('tickets')
        .select(
          `
          *,
          users!tickets_created_by_fkey (
            id,
            clerk_id,
            first_name,
            last_name,
            email,
            role,
            avatar_url
          ),
          assigned_user:users!tickets_assigned_to_fkey (
            id,
            clerk_id,
            first_name,
            last_name,
            email,
            role,
            avatar_url
          )
        `
        )
        .eq('tenant_id', tenantUuid)
        .order('created_at', { ascending: false });

      // Apply role-based filtering
      if (stableFilterOptions?.roleFilter === 'new') {
        query = query.is('assigned_to', null);
      } else if (stableFilterOptions?.roleFilter === 'assigned') {
        // CRITICAL FIX: For assigned tickets, fetch all assigned tickets and let frontend filtering handle role-based logic
        // The assignment metadata (assigned_by) is stored in JSON field, making server-side filtering complex
        if (context.role === 'admin' || context.role === 'super_admin') {
          // Both admin and super_admin see all assigned tickets, frontend filtering will handle the specifics
          query = query.not('assigned_to', 'is', null);
        } else if (context.role === 'agent' && context.userId) {
          // Agents only see tickets assigned to them (using database UUID)
          query = query.eq('assigned_to', context.userId);
        }
      }

      // Apply status filtering
      if (stableFilterOptions?.status) {
        query = query.eq('status', stableFilterOptions.status);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ Error fetching tickets:', error);
        throw error;
      }

      console.log('✅ Fetched tickets:', data?.length || 0);

      // Transform raw database data to match Ticket schema format
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const transformedTickets = (data || []).map((ticket: any) => ({
        id: ticket.id as string,
        tenantId: context.tenantId,
        title: ticket.title as string,
        description: ticket.description as string,
        status: ticket.status as string,
        priority: ticket.priority as string,
        department: ticket.department as string,
        createdAt: new Date(ticket.created_at as string),
        updatedAt: new Date(ticket.updated_at as string),
        userId: ticket.created_by as string,
        userName: ticket.users
          ? `${ticket.users.first_name || ''} ${ticket.users.last_name || ''}`.trim() ||
            'Unknown User'
          : 'Unknown User',
        userEmail: ticket.users?.email || '<EMAIL>',
        userAvatar: ticket.users?.avatar_url,
        messages: [],
        attachments: [],
        assignedTo: ticket.assigned_to as string | undefined,
        assignedUser: ticket.assigned_user
          ? {
              id: ticket.assigned_user.id,
              name: `${ticket.assigned_user.first_name || ''} ${ticket.assigned_user.last_name || ''}`.trim(),
              email: ticket.assigned_user.email,
              role: ticket.assigned_user.role,
              avatar: ticket.assigned_user.avatar_url,
            }
          : null,
        dueDate: ticket.due_date
          ? new Date(ticket.due_date as string)
          : undefined,
        resolvedAt: ticket.resolved_at
          ? new Date(ticket.resolved_at as string)
          : undefined,
        closedAt: ticket.closed_at
          ? new Date(ticket.closed_at as string)
          : undefined,
        tags: ticket.tags || [],
        metadata: ticket.metadata || {},
      }));

      return transformedTickets;
    },
    'tickets',
    {
      filter: tenantUuid
        ? `tenant_id=eq.${tenantUuid}`
        : 'tenant_id=eq.placeholder',
      queryOptions: {
        staleTime: 1000 * 60 * 5, // 5 minutes
        gcTime: 1000 * 60 * 30, // 30 minutes (formerly cacheTime)
        enabled: enabled && !!tenantUuid && !tenantUuidQuery.isLoading,
      },
    }
  );
}

/**
 * Interface for test entries
 */
export interface TestEntry {
  id: string;
  title: string;
  description: string;
  created_at: string;
  tenant_id: string;
}

/**
 * Specialized hook for the test table
 */
export function useRealtimeTest(tenantId: string) {
  const { supabase } = useSupabaseClient();

  return useRealtimeQuery<TestEntry>(
    ['realtime-test', tenantId],
    async () => {
      console.log('🔍 Fetching test entries for tenant:', tenantId);
      // Use type assertion to bypass TypeScript schema inference issues
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data, error } = await (supabase as any)
        .from('realtime_test')
        .select('id, title, description, created_at, tenant_id')
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching test entries:', error);
        throw error;
      }

      console.log('✅ Fetched test entries:', data?.length || 0);
      return (data || []) as TestEntry[];
    },
    'realtime_test',
    {
      filter: `tenant_id=eq.${tenantId}`,
      queryOptions: {
        staleTime: 0, // Always fresh for testing
        gcTime: 1000 * 60 * 5, // 5 minutes
      },
    }
  );
}
