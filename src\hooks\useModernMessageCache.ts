/**
 * Modern Message Cache Hook - 2025 Best Practices
 *
 * Uses useLiveQuery for reactive queries and integrates with React Query's persistence
 * Follows "update local cache first" pattern from Dexie.md documentation
 */

import { useLiveQuery } from 'dexie-react-hooks';
import { useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import {
  modernCacheDB,
  ModernMessageCache,
  type InputMessageData,
} from '@/lib/cache/modern-dexie-cache';
import { useTenant } from '@/features/tenant/store/use-tenant-store';

interface UseModernMessageCacheProps {
  ticketId: string;
  enabled?: boolean;
}

/**
 * Modern cache hook using 2025 best practices:
 * 1. useLiveQuery for reactive Dexie queries
 * 2. React Query handles server sync automatically
 * 3. "Update local cache first" pattern
 * 4. Minimal boilerplate, maximum performance
 */
export function useModernMessageCache({
  ticketId,
  enabled = true,
}: UseModernMessageCacheProps) {
  const { tenantId } = useTenant();
  const queryClient = useQueryClient();

  // Reactive query using useLiveQuery - updates automatically when Dexie data changes
  const cachedMessages = useLiveQuery(
    async () => {
      if (!enabled || !tenantId || !ticketId) return [];

      const messages = await modernCacheDB.messages
        .where('[tenant_id+ticket_id]')
        .equals([tenantId, ticketId])
        .toArray();

      // Sort by created_at since Dexie compound index doesn't guarantee order
      return messages.sort(
        (a, b) =>
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );
    },
    [tenantId, ticketId, enabled],
    [] // Default value while loading
  );

  // Check if we have expanded cache (reactive)
  const hasExpandedCache = useLiveQuery(
    async () => {
      if (!enabled || !tenantId || !ticketId) return false;

      const expandedMessages = await modernCacheDB.messages
        .where('[tenant_id+ticket_id]')
        .equals([tenantId, ticketId])
        .and((msg) => msg.is_expanded === true)
        .count();

      return expandedMessages > 0;
    },
    [tenantId, ticketId, enabled],
    false
  );

  // Get message count (reactive)
  const messageCount = useLiveQuery(
    async () => {
      if (!enabled || !tenantId || !ticketId) return 0;

      return await modernCacheDB.messages
        .where('[tenant_id+ticket_id]')
        .equals([tenantId, ticketId])
        .count();
    },
    [tenantId, ticketId, enabled],
    0
  );

  /**
   * Cache initial messages using "update local cache first" pattern
   */
  const cacheInitialMessages = useCallback(
    async (messages: InputMessageData[]) => {
      if (!tenantId) return;

      // Update local cache first
      await ModernMessageCache.cacheInitialMessages(
        tenantId,
        ticketId,
        messages
      );

      // React Query will handle server sync automatically
      // useLiveQuery will automatically update UI when Dexie data changes
      console.log(`📦 Cached initial messages: ${messages.length} messages`);
    },
    [tenantId, ticketId]
  );

  /**
   * Incremental sync - only fetch changes since last sync (2025 minimal approach)
   */
  const syncIncrementalChanges = useCallback(
    async (lastSyncTimestamp?: number) => {
      if (!tenantId) return;

      const since = lastSyncTimestamp || Date.now() - 30 * 60 * 1000; // Default: last 30 min

      // Update Dexie first, React Query handles server sync automatically
      await ModernMessageCache.syncChanges(tenantId, ticketId, since);

      // Invalidate React Query cache for fresh data
      queryClient.invalidateQueries({ queryKey: ['messages', ticketId] });

      console.log(
        `🔄 Synced incremental changes since: ${new Date(since).toISOString()}`
      );
    },
    [tenantId, ticketId, queryClient]
  );

  /**
   * Cache expanded messages using "update local cache first" pattern
   */
  const cacheExpandedMessages = useCallback(
    async (allMessages: InputMessageData[]) => {
      if (!tenantId) return;

      // Update local cache first
      await ModernMessageCache.cacheExpandedMessages(
        tenantId,
        ticketId,
        allMessages
      );

      // useLiveQuery will automatically update UI
      console.log(
        `📦 Cached expanded messages: ${allMessages.length} messages`
      );
    },
    [tenantId, ticketId]
  );

  /**
   * Add new message (for real-time updates)
   */
  const addMessage = useCallback(
    async (message: InputMessageData) => {
      if (!tenantId) return;

      // Update local cache first
      await ModernMessageCache.addMessage(tenantId, ticketId, message);

      // Invalidate React Query to trigger server sync
      queryClient.invalidateQueries({
        queryKey: ['ticket-messages', ticketId],
      });

      console.log(`📦 Added new message to cache: ${message.id}`);
    },
    [tenantId, ticketId, queryClient]
  );

  /**
   * Update existing message
   */
  const updateMessage = useCallback(
    async (messageId: string, updates: Partial<InputMessageData>) => {
      // Update local cache first
      await ModernMessageCache.updateMessage(messageId, updates);

      // Invalidate React Query to trigger server sync
      queryClient.invalidateQueries({
        queryKey: ['ticket-messages', ticketId],
      });

      console.log(`📦 Updated message in cache: ${messageId}`);
    },
    [ticketId, queryClient]
  );

  /**
   * Delete message
   */
  const deleteMessage = useCallback(
    async (messageId: string) => {
      // Update local cache first
      await ModernMessageCache.deleteMessage(messageId);

      // Invalidate React Query to trigger server sync
      queryClient.invalidateQueries({
        queryKey: ['ticket-messages', ticketId],
      });

      console.log(`📦 Deleted message from cache: ${messageId}`);
    },
    [ticketId, queryClient]
  );

  /**
   * Clear ticket cache
   */
  const clearTicketCache = useCallback(async () => {
    if (!tenantId) return;

    await ModernMessageCache.clearTicketCache(tenantId, ticketId);

    // Invalidate React Query
    queryClient.invalidateQueries({
      queryKey: ['ticket-messages', ticketId],
    });

    console.log(`🗑️ Cleared cache for ticket: ${ticketId}`);
  }, [tenantId, ticketId, queryClient]);

  /**
   * Real-time updates integration - 2025 minimal approach
   * Handles messages, status, priority changes with React Query sync
   */
  const handleRealtimeUpdate = useCallback(
    async (event: {
      type: 'INSERT' | 'UPDATE' | 'DELETE';
      table: 'messages' | 'tickets';
      new?:
        | InputMessageData
        | { id: string; status?: string; priority?: string };
      old?: InputMessageData | { id: string };
    }) => {
      // Update Dexie first (local cache)
      switch (event.type) {
        case 'INSERT':
          if (
            event.table === 'messages' &&
            event.new &&
            'content' in event.new
          ) {
            await addMessage(event.new as InputMessageData);
          }
          break;

        case 'UPDATE':
          if (
            event.table === 'messages' &&
            event.new &&
            'content' in event.new
          ) {
            await updateMessage(event.new.id, event.new as InputMessageData);
          }
          break;

        case 'DELETE':
          if (event.table === 'messages' && event.old) {
            await deleteMessage(event.old.id);
          }
          break;
      }

      // Invalidate React Query cache (server sync)
      if (event.table === 'messages') {
        queryClient.invalidateQueries({ queryKey: ['messages', ticketId] });
      } else if (event.table === 'tickets') {
        queryClient.invalidateQueries({ queryKey: ['tickets', ticketId] });
      }

      console.log(`🔄 Real-time ${event.type} on ${event.table}`);
    },
    [addMessage, updateMessage, deleteMessage, queryClient, ticketId]
  );

  return {
    // Reactive data (automatically updates when Dexie data changes)
    cachedMessages: cachedMessages || [],
    hasExpandedCache: hasExpandedCache || false,
    messageCount: messageCount || 0,

    // Cache operations using "update local cache first" pattern
    cacheInitialMessages,
    cacheExpandedMessages,
    addMessage,
    updateMessage,
    deleteMessage,
    clearTicketCache,

    // 2025 minimal approach functions
    syncIncrementalChanges,
    handleRealtimeUpdate,

    // Status
    isLoading: cachedMessages === undefined,
    isEmpty: (cachedMessages?.length || 0) === 0,
  };
}
